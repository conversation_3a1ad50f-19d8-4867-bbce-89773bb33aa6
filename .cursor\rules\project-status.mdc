---
description: 项目情况
globs:
alwaysApply: true
---
# 图书管理系统技术文档


## 一、项目概述

### 1.1 系统定位

图书管理系统是一个基于SpringBoot和Vue的全栈应用，旨在提供完整的图书管理解决方案，包括图书借阅、归还、预约、订阅等核心功能，满足图书馆日常运营需求。

### 1.2 核心功能亮点

- **完整的借阅流程**：支持用户查找、借阅、归还图书的全流程管理
- **订阅通知系统**：用户可订阅未收录的图书，系统自动发送到货通知
- **用户行为日志**：基于AOP技术的用户行为全程记录，方便追踪与分析
- **多维度图书管理**：包括书籍分类、书架管理等多角度的图书资源管理
- **可视化统计报表**：管理员首页提供图书借阅、用户活跃度等数据可视化展示
- **个性化图书推荐**：基于多维度用户行为分析的智能推荐系统，结合时间衰减因子和标签均衡机制，提供精准的个性化推荐、热门借阅和新书速递功能
- **社区化留言系统**：楼中楼评论结构，支持点赞、收藏、标签分类、通知消息，管理员可设置置顶与精华，提升用户互动与社区活跃度

### 1.3 用户角色划分

- **管理员**：负责系统管理、图书管理、用户管理等后台操作
- **普通用户**：进行图书借阅、归还、收藏、订阅等日常操作

## 二、技术架构

### 2.1 整体架构图

```
┌─────────────────┐      ┌─────────────────┐      ┌─────────────────┐
│                 │      │                 │      │                 │
│  前端展示层     │─────▶│  后端服务层     │─────▶│  数据持久层     │
│  Vue + ElementUI│      │  SpringBoot     │      │  MySQL          │
│                 │◀─────│  + SpringMVC    │◀─────│                 │
└─────────────────┘      └─────────────────┘      └─────────────────┘
```

### 2.2 技术栈选型表

| 层级       | 技术组件                            |
|------------|-----------------------------------|
| 前端框架   | Vue.js, Axios, Vue Router, Element UI |
| 后端框架   | SpringBoot, SpringMVC, MyBatis    |
| 数据存储   | MySQL 8.0.34                      |
| 基础设施   | JDK 1.8, Maven 3.6.1, Node.js 14.16.0 |

## 三、项目结构

### 3.1 后端结构

```
后端/books-manage-sys/src/main/java/cn/kmbeast/
├── controller/   # API接口层
│   ├── BookController.java
│   ├── BookOrderHistoryController.java
│   ├── BookRssHistoryController.java
│   ├── BookSaveController.java
│   ├── BookshelfController.java
│   ├── CategoryController.java
│   ├── FileController.java
│   ├── NoticeController.java
│   ├── ReaderProposalController.java
│   ├── ReaderProposalCategoryController.java
│   ├── RssNotificationController.java
│   ├── SimpleRecommendationController.java
│   ├── UserController.java
│   ├── UserOperationLogController.java
│   └── ViewsController.java
├── service/      # 业务逻辑层
│   ├── impl/     # 接口实现
│   │   ├── BookOrderHistoryServiceImpl.java
│   │   ├── BookRssHistoryServiceImpl.java
│   │   ├── BookSaveServiceImpl.java
│   │   ├── BookServiceImpl.java
│   │   ├── BookshelfServiceImpl.java
│   │   ├── CategoryServiceImpl.java
│   │   ├── NoticeServiceImpl.java
│   │   ├── ReaderProposalServiceImpl.java
│   │   ├── ReaderProposalCategoryServiceImpl.java
│   │   ├── RssNotificationServiceImpl.java
│   │   ├── SimpleRecommendationServiceImpl.java
│   │   ├── UserOperationLogServiceImpl.java
│   │   ├── UserServiceImpl.java
│   │   └── ViewsServiceImpl.java
│   ├── BookOrderHistoryService.java
│   ├── BookRssHistoryService.java
│   ├── BookSaveService.java
│   ├── BookService.java
│   ├── BookshelfService.java
│   ├── CategoryService.java
│   ├── NoticeService.java
│   ├── ReaderProposalService.java
│   ├── ReaderProposalCategoryService.java
│   ├── RssNotificationService.java
│   ├── SimpleRecommendationService.java
│   ├── UserOperationLogService.java
│   ├── UserService.java
│   └── ViewsService.java
├── mapper/       # 数据持久层
│   ├── BookMapper.java
│   ├── BookOrderHistoryMapper.java
│   ├── BookRssHistoryMapper.java
│   ├── BookSaveMapper.java
│   ├── BookShelfMapper.java
│   ├── CategoryMapper.java
│   ├── NoticeMapper.java
│   ├── ReaderProposalMapper.java
│   ├── ReaderProposalCategoryMapper.java
│   ├── ReaderProposalLikeMapper.java
│   ├── ReaderProposalSaveMapper.java
│   ├── RssNotificationMapper.java
│   ├── UserMapper.java
│   └── UserOperationLogMapper.java
├── resources/    # 资源文件
│   ├── mapper/   # MyBatis映射文件
│   │   ├── BookMapper.xml            # 包含图书推荐相关查询
│   │   ├── BookOrderHistoryMapper.xml # 包含用户借阅历史分析
│   │   ├── BookSaveMapper.xml        # 包含收藏统计查询
│   │   ├── ReaderProposalMapper.xml  # 留言系统相关查询
│   │   └── 其他Mapper文件
├── pojo/         # 数据对象
│   ├── entity/   # 数据实体
│   │   ├── Book.java
│   │   ├── BookOrderHistory.java
│   │   ├── BookRssHistory.java
│   │   ├── BookSave.java
│   │   ├── BookShelf.java
│   │   ├── Category.java
│   │   ├── Notice.java
│   │   ├── ReaderProposal.java
│   │   ├── ReaderProposalCategory.java
│   │   ├── RssNotification.java
│   │   ├── User.java
│   │   └── UserOperationLog.java
│   ├── vo/       # 视图对象
│   │   ├── BookOrderHistoryVO.java
│   │   ├── BookRssHistoryVO.java
│   │   ├── BookSaveVO.java
│   │   ├── BookVO.java
│   │   ├── ChartVO.java
│   │   ├── ReaderProposalVO.java
│   │   ├── ReaderProposalCategoryVO.java
│   │   ├── RssNotificationVO.java
│   │   ├── UserOperationLogVO.java
│   │   └── UserVO.java
│   ├── dto/      # 数据传输对象
│   │   ├── query/ # 查询条件类
│   │   │   ├── extend/
│   │   │   │   ├── BookOrderHistoryQueryDto.java
│   │   │   │   ├── BookQueryDto.java
│   │   │   │   ├── BookRssHistoryQueryDto.java
│   │   │   │   ├── BookSaveQueryDto.java
│   │   │   │   ├── BookshelfQueryDto.java
│   │   │   │   ├── CategoryQueryDto.java
│   │   │   │   ├── ReaderProposalQueryDto.java
│   │   │   │   ├── RssNotificationQueryDto.java
│   │   │   │   └── UserOperationLogQueryDto.java
│   ├── api/      # API响应对象
├── aop/          # AOP切面
│   ├── Log.java
│   ├── LogAspect.java
│   ├── Pager.java
│   ├── PagerAspect.java
│   ├── Protector.java
│   └── ProtectorAspect.java
├── config/       # 配置类
├── context/      # 上下文管理
│   └── LocalThreadHolder.java
└── utils/        # 工具类
```

### 3.2 前端结构

```
src/
├── views/                # 页面组件
│   ├── admin/            # 管理员页面
│   │   ├── BookManage.vue
│   │   ├── BookOrderHistoryManage.vue
│   │   ├── BookRssHistoryManage.vue
│   │   ├── BookshelfManage.vue
│   │   ├── CategoryManage.vue
│   │   ├── CreateNotice.vue
│   │   ├── Home.vue
│   │   ├── Main.vue
│   │   ├── NoticeManage.vue
│   │   ├── ReaderProposalManage.vue
│   │   ├── ReaderProposalCategoryManage.vue
│   │   ├── RssNotificationManage.vue
│   │   ├── UserManage.vue
│   │   └── UserOperationLogManage.vue
│   ├── user/             # 用户页面
│   │   ├── BookOperation.vue
│   │   ├── BookOrderHistory.vue
│   │   ├── BookRssHistory.vue
│   │   ├── BookSave.vue
│   │   ├── Home.vue
│   │   ├── Main.vue
│   │   ├── MySelf.vue
│   │   ├── Notice.vue
│   │   ├── NoticeDetail.vue
│   │   ├── ReaderProposal.vue
│   │   ├── ResetPwd.vue
│   │   ├── RssNotification.vue
│   │   ├── Self.vue
│   │   ├── UserDashboard.vue  # 用户首页(包含个性化推荐界面)
│   │   └── UserOperationLog.vue
│   ├── login/            # 登录相关页面
│   └── register/         # 注册相关页面
├── router/               # 路由配置
├── components/           # 公共组件
│   ├── BookCard.vue      # 图书卡片组件(含3D翻转效果)
│   ├── VerticalMenu.vue
│   ├── PieChart.vue
│   ├── LevelHeader.vue
│   ├── LineChart.vue
│   ├── LevelMenu.vue
│   ├── Logo.vue
│   ├── Editor.vue
│   ├── Evaluations.vue
│   ├── Inteval.vue
│   ├── TagLine.vue
│   └── BarChart.vue
├── utils/                # 工具函数
│   ├── eventBus.js       # 事件总线，用于组件间通信
│   └── 其他工具函数
└── assets/               # 静态资源
```

### 3.3 前端组件设计

- **公共组件**：位于`components`目录，提供通用UI元素
- **布局模式**：采用Element UI的栅格系统和Flex布局
- **状态管理**：使用Vue Router进行路由管理，eventBus实现组件间通信
- **API封装**：通过Axios实例统一处理请求和响应
- **组件复用**：页面组件模块化设计，实现代码复用和维护简化

## 四、核心模块设计

### 4.1 核心业务流程图

```
[用户] → [查找图书] → [借阅图书] → [借阅记录] → [归还图书]
                   ↘ [收藏图书] → [收藏记录]
                   ↘ [订阅未有图书] → [订阅记录] → [接收通知]
                   ↘ [参与社区讨论] → [发布/回复留言] → [收到互动通知]

[管理员] → [图书管理] → [新增/编辑/删除图书]
         → [书架管理] → [分配图书位置]
         → [分类管理] → [维护图书分类]
         → [借阅管理] → [查看借阅情况]
         → [订阅管理] → [处理订阅请求/发送通知]
         → [社区管理] → [设置置顶/精华/分类标签]
         → [读者反馈] → [处理用户建议]
         → [数据统计] → [查看系统使用情况]
```

### 4.2 典型功能实现

#### 4.2.1 图书借阅流程

- **控制器**：`BookOrderHistoryController.java`
- **服务层**：`BookOrderHistoryService.java`、`BookOrderHistoryServiceImpl.java`
- **数据访问**：`BookOrderHistoryMapper.java`
- **前端界面**：`BookOperation.vue`（用户）、`BookOrderHistoryManage.vue`（管理员）

借阅流程涉及库存检查、借阅记录创建、图书状态更新等多个环节，通过事务确保数据一致性。

#### 4.2.2 图书订阅通知机制

- **控制器**：`BookRssHistoryController.java`、`RssNotificationController.java`
- **服务层**：`BookRssHistoryService.java`、`RssNotificationService.java`
- **服务实现**：`BookRssHistoryServiceImpl.java`、`RssNotificationServiceImpl.java`
- **前端界面**：`BookRssHistory.vue`、`RssNotification.vue`

用户可以订阅尚未入库的图书，当图书入库后，系统自动向相关用户发送通知。

#### 4.2.3 个性化图书推荐系统

- **控制器**：`SimpleRecommendationController.java`
- **服务层**：`SimpleRecommendationService.java`、`SimpleRecommendationServiceImpl.java`
- **数据访问**：`BookMapper.java`、`BookOrderHistoryMapper.java`、`BookSaveMapper.java`、`BookRssHistoryMapper.java`
- **前端组件**：`UserDashboard.vue`（用户仪表盘）、`BookCard.vue`（图书卡片组件）
- **推荐策略**：
  - **个性化推荐**：多维度用户行为分析，整合借阅历史(权重0.6)、收藏记录(权重0.3)和订阅记录(权重0.1)
  - **热门借阅**：基于借阅量统计的热门图书
  - **新书速递**：最新入库的图书推荐
  - **时间衰减因子**：使近期行为影响更大，采用指数衰减公式
  - **标签均衡机制**：确保不同类型标签（"热门"、"常读"、"新书"、"作者"、"出版社"）均衡展示
  - **多级推荐备选方案**：主推荐、备选方案1和备选方案2，确保推荐质量
  - **推荐结果随机化**：采用分类交错策略，使不同分类的书籍交替展示，提升多样性

系统通过分析用户多种行为数据，识别用户偏好的图书类别，实现精准推荐。支持"换一批推荐"功能，无需刷新整页即可获取新推荐，同时保持用户滚动位置。对于新用户，默认展示热门图书，随着用户使用系统积累借阅记录，推荐会更加个性化。

**增强组件：**

- **图书卡片：** 实现3D翻转效果，包含精美上浮动画、仿真书籍效果(书脊、装订线和纸张质感)
- **标签体系：** 根据推荐原因自动分配标签颜色：热门(红色)、常读(绿色)、新书(黄色)、作者(蓝色)、出版社(浅色)
- **交互体验：** 鼠标悬停时封面图自动上浮，点击可翻转查看详情，书签式返回按钮

**数据模型扩展：**
```java
// 用户分类偏好类
public class CategoryPreference {
    private Long categoryId;     // 分类ID
    private String categoryName; // 分类名称
    private Integer count;       // 行为次数
    private Date latestTime;     // 最近行为时间
}

// 标签配额管理类
class RecommendOption {
    String reason;  // 推荐原因
    String type;    // 标签类型
    int priority;   // 优先级
}
```

**数据库优化：**
```sql
-- 增加索引提升查询性能
ALTER TABLE book_save ADD INDEX idx_book_save_user_id (user_id);
ALTER TABLE book_save ADD INDEX idx_book_save_book_id (book_id);
ALTER TABLE book_rss_history ADD INDEX idx_book_rss_user_id (user_id);
ALTER TABLE book_rss_history ADD INDEX idx_book_rss_book_id (book_id);
ALTER TABLE book_rss_history ADD INDEX idx_book_rss_create_time (create_time);
```

#### 4.2.4 社区化留言系统

- **控制器**：`ReaderProposalController.java`、`ReaderProposalCategoryController.java`
- **服务层**：`ReaderProposalService.java`、`ReaderProposalCategoryService.java`
- **服务实现**：`ReaderProposalServiceImpl.java`、`ReaderProposalCategoryServiceImpl.java`
- **数据访问**：`ReaderProposalMapper.java`、`ReaderProposalCategoryMapper.java`、`ReaderProposalLikeMapper.java`、`ReaderProposalSaveMapper.java`
- **前端组件**：`Main.vue`（用户）、`ReaderProposal.vue`（用户）、`ReaderProposalManage.vue`（管理员）、`ReaderProposalCategoryManage.vue`（管理员）

社区留言系统支持楼中楼评论结构，实现了帖子发布、回复、点赞、收藏等功能，通过事件总线实现通知计数的跨组件更新。

**核心功能：**

- **楼中楼回复结构**：支持二级回复层级，通过视觉设计展示回复关系
- **回复高亮定位**：通过回复ID精确定位，脉冲动画引导用户注意
- **帖主与管理员标识**：直观区分原帖作者和管理员回复
- **消息通知系统**：支持已读/未读状态管理，实时更新通知计数
- **分类标签系统**：支持自定义标签名称和颜色，增强视觉区分度
- **双模式管理界面**：管理员可在"只看帖子"和"用户回复通知"两种模式间切换
- **批量操作功能**：支持批量删除、置顶、加精和设置标签

**数据模型：**
```java
// 读者建议实体
public class ReaderProposal {
    private Integer id;
    private Integer userId;
    private String content;
    private Boolean isPublish;
    private String replyContent;
    private Boolean isReply;
    private LocalDateTime replyTime;
    private LocalDateTime createTime;
    private Integer categoryId;
    private Integer parentId;
    private Integer likeCount;
    private Boolean isTop;
    private Boolean isEssence;
    private Integer replyCount;
    private Integer replyToId;
    private Integer replyToUserId;
    private String replyToUserName;
    private Integer replyLevel;
    private Boolean isPostAuthor;
}

// 读者建议分类
public class ReaderProposalCategory {
    private Integer id;
    private String name;
    private String color;
    private LocalDateTime createTime;
}
```

**索引优化：**
```sql
-- 优化留言查询性能
ALTER TABLE reader_proposal ADD INDEX idx_rp_user_id (user_id);
ALTER TABLE reader_proposal ADD INDEX idx_rp_parent_id (parent_id);
ALTER TABLE reader_proposal ADD INDEX idx_rp_category_id (category_id);
ALTER TABLE reader_proposal ADD INDEX idx_rp_create_time (create_time);
```

#### 4.2.5 用户行为日志记录

- **AOP注解**：`Log.java`
- **AOP切面**：`LogAspect.java`
- **服务层**：`UserOperationLogService.java`、`UserOperationLogServiceImpl.java`
- **数据访问**：`UserOperationLogMapper.java`
- **前端界面**：`UserOperationLog.vue`

采用AOP技术，通过注解方式对用户关键操作进行日志记录，实现用户行为的全程跟踪。

#### 4.2.6 文件上传管理

- **控制器**：`FileController.java`
- **前端调用**：主要在`BookManage.vue`中使用
- **功能描述**：提供图书封面图片等文件的上传、删除功能，支持图片预览
- **存储机制**：文件存储在服务器指定目录，数据库中仅保存路径引用

#### 4.2.7 数据可视化统计

- **控制器**：`ViewsController.java`
- **服务层**：`ViewsService.java`、`ViewsServiceImpl.java`
- **前端实现**：`admin/Main.vue`中使用图表组件
- **数据来源**：通过`ChartVO.java`封装统计数据
- **统计内容**：包括借阅量、用户活跃度、书籍分类分布等关键指标

## 五、关键技术方案

### 5.1 异常处理机制

系统采用统一的异常处理机制，通过全局异常处理器捕获并处理各类异常，确保API返回格式一致，提升系统健壮性。异常处理结果通过 `pojo.api.Result` 类进行统一封装。

### 5.2 权限控制方案

基于角色的访问控制（RBAC），结合拦截器（`Interceptor`目录下）进行认证和授权。系统区分管理员和普通用户权限，通过 `LocalThreadHolder` 上下文管理用户信息。

### 5.3 数据验证策略

- **前端验证**：使用Element UI表单验证规则
- **后端验证**：使用参数校验，以及AOP方式的 `ProtectorAspect.java` 进行参数保护
- **统一响应**：通过 `pojo.api.ApiResult` 和 `pojo.api.Result` 进行响应封装

### 5.4 日志监控体系

- **技术选型**：基于Spring AOP实现
- **记录方式**：通过`@Log`注解标记需要记录日志的方法
- **记录内容**：操作用户、操作类型、操作内容、操作时间等关键信息
- **存储机制**：通过 `UserOperationLogMapper` 将日志保存到数据库
- **查询展示**：支持按用户、时间、操作类型等多维度查询筛选

### 5.5 分页实现方案

- **技术实现**：基于AOP实现的统一分页
- **核心组件**：`Pager.java`（注解）和`PagerAspect.java`（切面）
- **使用方式**：在Controller方法上添加`@Pager`注解自动实现分页处理
- **优势**：减少重复代码，提高开发效率，统一分页逻辑
- **响应封装**：通过`PageResult`类封装分页结果

### 5.6 安全防护机制

- **技术实现**：基于AOP的请求保护
- **核心组件**：`Protector.java`（注解）和`ProtectorAspect.java`（切面）
- **防护内容**：防止重复提交、参数验证、权限检查等
- **工作原理**：通过切面拦截带有`@Protector`注解的方法，进行前置检查
- **应用范围**：主要应用于数据修改和敏感操作接口

### 5.7 跨域处理方案

- **实现方式**：通过配置类和拦截器处理跨域请求
- **配置位置**：在`config`包中的Web配置类
- **支持方法**：支持GET、POST、PUT、DELETE等HTTP方法
- **头部处理**：允许自定义头部和凭证传递
- **安全控制**：结合权限控制，确保跨域请求的安全性

### 5.8 数据库设计

系统采用MySQL数据库，主要表结构如下：

#### 5.8.1 核心表设计

- **book**：图书信息表
  - `id`：主键
  - `name`：书名
  - `cover`：封面图片路径
  - `publisher`：出版社
  - `author`：作者
  - `isbn`：ISBN编号
  - `num`：库存数量
  - `detail`：详细描述
  - `bookshelf_id`：书架ID（外键）
  - `category_id`：分类ID（外键）
  - `is_plan_buy`：是否计划购买
  - `plan_buy_time`：计划购买时间
  - `create_time`：创建时间

- **category**：图书分类表
  - `id`：主键
  - `name`：分类名称
  - `create_time`：创建时间

- **bookshelf**：书架信息表
  - `id`：主键
  - `floor`：楼层
  - `area`：区域
  - `frame`：架号
  - `create_time`：创建时间

#### 5.8.2 借阅与推荐相关表

- **book_order_history**：图书借阅历史表
  - `id`：主键
  - `book_id`：图书ID（外键）
  - `user_id`：用户ID（外键）
  - `deadline_num`：借阅数量
  - `is_return`：是否归还（0未归还，1已归还）
  - `return_time`：归还时间
  - `create_time`：借阅时间

  *该表用于个性化推荐算法，通过分析用户借阅历史确定用户喜好*

- **book_save**：图书收藏表
  - `id`：主键
  - `book_id`：图书ID（外键）
  - `user_id`：用户ID（外键）
  - `create_time`：收藏时间

  *用于统计用户收藏情况，辅助用户兴趣分析*

#### 5.8.3 订阅与通知表

- **book_rss_history**：图书订阅历史表
  - `id`：主键
  - `book_id`：图书ID（外键）
  - `user_id`：用户ID（外键）
  - `create_time`：订阅时间

- **rss_notification**：订阅通知表
  - `id`：主键
  - `book_id`：图书ID（外键）
  - `user_id`：用户ID（外键）
  - `is_read`：是否已读
  - `create_time`：通知时间

#### 5.8.4 社区留言相关表

- **reader_proposal**：读者留言表
  - `id`：主键
  - `user_id`：用户ID
  - `content`：留言内容
  - `is_publish`：是否公开
  - `parent_id`：父留言ID（用于回复）
  - `reply_level`：回复层级（0原帖，1一级回复，2二级回复）
  - `reply_to_id`：回复目标ID
  - `reply_to_user_id`：被回复用户ID
  - `reply_to_user_name`：被回复用户名称
  - `is_post_author`：是否为帖主
  - `category_id`：分类标签ID
  - `like_count`：点赞数
  - `reply_count`：回复数
  - `is_top`：是否置顶
  - `is_essence`：是否精华
  - `create_time`：创建时间

- **reader_proposal_category**：留言分类表
  - `id`：主键
  - `name`：分类名称
  - `color`：分类颜色
  - `create_time`：创建时间

- **reader_proposal_like**：留言点赞关系表
  - `id`：主键
  - `proposal_id`：留言ID
  - `user_id`：用户ID
  - `create_time`：点赞时间

- **reader_proposal_save**：留言收藏关系表
  - `id`：主键
  - `proposal_id`：留言ID
  - `user_id`：用户ID
  - `create_time`：收藏时间

通过这些表的关联和分析，系统能够实现个性化图书推荐、热门图书统计、新书通知以及社区留言互动等功能，为用户提供精准的阅读推荐服务和良好的社区交流平台。

## 六、部署说明

### 6.1 环境依赖清单

- JDK 1.8+
- Maven 3.6.1+
- Node.js 14.16.0+
- MySQL 8.0.34
- npm 或 yarn

### 6.2 启动配置指引

**后端启动步骤**：
1. 克隆代码仓库
2. 配置`application.properties`中的数据库连接信息
3. 执行Maven命令：`mvn clean package`
4. 运行JAR包：`java -jar target/book-manage-sys.jar`

**前端启动步骤**：
1. 进入前端目录
2. 执行`npm install`安装依赖
3. 执行`npm run serve`启动开发服务器
4. 执行`npm run build`构建生产环境代码

### 6.3 常见问题排错

- **数据库连接异常**：检查MySQL服务状态和连接配置
- **前端API请求失败**：检查后端服务是否正常运行，以及跨域配置
- **权限验证失败**：检查用户登录状态和token有效性
- **图片上传失败**：检查 `FileController.java` 中的上传路径配置

## 七、附录

### 7.1 接口文档地址

系统API文档可通过项目中的控制器类查看，遵循RESTful风格设计。

### 7.2 代码规范说明

- 后端遵循驼峰命名法，分层清晰
- 前端遵循Vue官方风格指南
- 数据库表命名采用下划线分隔

### 7.3 版本更新记录

- V0.1：完成基础功能开发，包括图书管理、借阅、订阅等核心功能
- V0.2：优化用户界面，完善日志记录功能，增强数据统计展示
- V0.3：新增个性化图书推荐系统，包括用户个性化推荐、热门图书推荐和新书速递功能，优化用户首页体验
- V0.4：全面升级推荐系统，实现多维度用户行为分析、时间衰减因子和标签均衡机制；优化图书卡片组件，添加3D翻转效果和"换一批推荐"功能，提升用户体验
- V0.5：实现社区化留言系统，包括楼中楼回复结构、点赞收藏功能、标签分类系统和通知消息管理；添加二级回复、帖主标识、管理员回复标识等功能，大幅提升用户互动体验
