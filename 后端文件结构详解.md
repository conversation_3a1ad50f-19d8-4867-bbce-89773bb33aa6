# 📁 后端项目文件结构详解

## 🎯 **项目整体结构**

`后端/books-manage-sys/` 是一个标准的**Maven SpringBoot项目**，采用**三层架构**设计模式。

## 📂 **顶层文件和目录**

### **1. 📄 核心配置文件**

#### **`pom.xml` - Maven项目配置文件**
**作用：** 定义项目依赖、构建配置和项目信息
- 项目基本信息：groupId、artifactId、version
- SpringBoot父依赖：2.2.4.RELEASE
- 核心依赖：SpringBoot Web、MyBatis、JWT、MySQL等
- 构建配置：资源路径、编译设置
- 仓库配置：阿里云Maven仓库

#### **`book-manage-sys-api.iml` - IntelliJ IDEA项目配置文件**
**作用：** IDE的项目配置信息，包含模块设置、依赖路径等

### **2. 📂 源代码目录**

#### **`src/main/java/` - Java源代码目录**
**作用：** 存放所有Java源代码文件

#### **`src/main/resources/` - 资源文件目录**
**作用：** 存放配置文件、SQL映射文件等资源

### **3. 📂 编译输出目录**

#### **`target/` - Maven编译输出目录**
**作用：** 存放编译后的class文件和打包后的JAR文件
- `book-manage-sys-api-1.0-SNAPSHOT.jar` - 可执行的JAR包
- `classes/` - 编译后的class文件
- `maven-archiver/` - Maven打包信息

### **4. 📂 文件存储目录**

#### **`pic/` - 图片文件存储目录**
**作用：** 存储用户上传的图片文件
- 图书封面图片（如：图书1.png、图书3.png等）
- 用户头像图片（如：Multiavatar-*.png）
- 系统截图和其他图片资源

## 🏗️ **Java源代码结构详解**

### **📁 `src/main/java/cn/kmbeast/`**

#### **1. 🚀 启动类**
- **`BookManageSysApplication.java`** - SpringBoot应用启动类
  - 使用@SpringBootApplication注解
  - 配置@MapperScan扫描Mapper接口
  - 包含main方法启动应用

#### **2. 🎮 控制层 (`controller/`)**
**作用：** 处理HTTP请求，提供RESTful API接口

| 文件名 | 作用 |
|--------|------|
| `UserController.java` | 用户管理API（登录、注册、认证） |
| `BookController.java` | 图书管理API（增删改查） |
| `BookOrderHistoryController.java` | 借阅历史管理API |
| `BookSaveController.java` | 图书收藏管理API |
| `BookshelfController.java` | 书架管理API |
| `CategoryController.java` | 分类管理API |
| `ChartController.java` | 图表数据API |
| `FileController.java` | 文件上传下载API |
| `NoticeController.java` | 通知管理API |
| `ReaderProposalController.java` | 读者建议API |
| `RssNotificationController.java` | 订阅通知API |
| `SimpleRecommendationController.java` | 推荐算法API |
| `UserOperationLogController.java` | 用户操作日志API |
| `ViewsController.java` | 访问量统计API |

#### **3. 🔧 服务层 (`service/` 和 `service/impl/`)**
**作用：** 业务逻辑处理层

**接口定义 (`service/`)：**
- 定义业务方法的接口规范
- 如：UserService.java、BookService.java等

**实现类 (`service/impl/`)：**
- 具体的业务逻辑实现
- 如：UserServiceImpl.java、BookServiceImpl.java等

#### **4. 🗄️ 数据访问层 (`mapper/`)**
**作用：** 数据库访问接口，使用MyBatis框架

| 文件名 | 作用 |
|--------|------|
| `UserMapper.java` | 用户数据访问接口 |
| `BookMapper.java` | 图书数据访问接口 |
| `BookOrderHistoryMapper.java` | 借阅历史数据访问 |
| `BookSaveMapper.java` | 图书收藏数据访问 |
| `BookShelfMapper.java` | 书架数据访问接口 |
| `CategoryMapper.java` | 分类数据访问接口 |
| `NoticeMapper.java` | 通知数据访问接口 |
| `ReaderProposalMapper.java` | 读者建议数据访问 |
| `RssNotificationMapper.java` | 订阅通知数据访问 |
| `UserOperationLogMapper.java` | 用户操作日志数据访问 |

#### **5. 📦 数据对象 (`pojo/`)**

**实体类 (`entity/`)：**
- 数据库表对应的实体类
- 如：User.java、Book.java、BookOrderHistory.java等

**数据传输对象 (`dto/`)：**
- `query/` - 查询条件DTO
- `update/` - 更新数据DTO

**视图对象 (`vo/`)：**
- 返回给前端的数据对象
- 如：UserVO.java、BookVO.java等

**枚举类 (`em/`)：**
- 系统中使用的枚举定义
- 如：RoleEnum.java（角色枚举）、AuditStatusEnum.java（审核状态）

**API响应 (`api/`)：**
- Result.java - 统一响应格式
- ApiResult.java - API结果封装
- PageResult.java - 分页结果封装

#### **6. 🔧 配置类 (`config/`)**
**作用：** Spring配置类

| 文件名 | 作用 |
|--------|------|
| `InterceptorConfig.java` | 拦截器配置 |
| `WebConfig.java` | Web相关配置 |

#### **7. 🔍 切面编程 (`aop/`)**
**作用：** 横切关注点处理

| 文件名 | 作用 |
|--------|------|
| `LogAspect.java` | 日志记录切面 |
| `PagerAspect.java` | 分页处理切面 |
| `ProtectorAspect.java` | 权限保护切面 |
| `@Log` | 日志注解 |
| `@Pager` | 分页注解 |
| `@Protector` | 权限保护注解 |

#### **8. 🚫 拦截器 (`Interceptor/`)**
**作用：** 请求拦截处理

| 文件名 | 作用 |
|--------|------|
| `JwtInterceptor.java` | JWT令牌验证拦截器 |

#### **9. 🧵 上下文管理 (`context/`)**
**作用：** 线程上下文管理

| 文件名 | 作用 |
|--------|------|
| `LocalThreadHolder.java` | 线程本地变量管理 |

#### **10. 🛠️ 工具类 (`utils/`)**
**作用：** 通用工具方法

| 文件名 | 作用 |
|--------|------|
| `JwtUtil.java` | JWT令牌工具类 |
| `DateUtil.java` | 日期处理工具类 |
| `IdFactoryUtil.java` | ID生成工具类 |
| `PathUtils.java` | 路径处理工具类 |

## 🗄️ **资源文件结构详解**

### **📁 `src/main/resources/`**

#### **1. 📄 配置文件**
- **`application.yml`** - SpringBoot应用配置文件
  - 服务器端口配置（21090）
  - 数据库连接配置
  - MyBatis配置

#### **2. 📁 SQL映射文件 (`mapper/`)**
**作用：** MyBatis的XML映射文件

| 文件名 | 作用 |
|--------|------|
| `UserMapper.xml` | 用户相关SQL映射 |
| `BookMapper.xml` | 图书相关SQL映射 |
| `BookOrderHistoryMapper.xml` | 借阅历史SQL映射 |
| `BookSaveMapper.xml` | 图书收藏SQL映射 |
| `BookshelfMapper.xml` | 书架相关SQL映射 |
| `CategoryMapper.xml` | 分类相关SQL映射 |
| `NoticeMapper.xml` | 通知相关SQL映射 |
| `ReaderProposalMapper.xml` | 读者建议SQL映射 |
| `RssNotificationMapper.xml` | 订阅通知SQL映射 |
| `UserOperationLogMapper.xml` | 用户操作日志SQL映射 |

#### **3. 📁 数据库脚本 (`db/`)**
**作用：** 数据库初始化脚本（如果存在）

## 🔄 **项目运行流程**

1. **启动：** BookManageSysApplication.main() 启动SpringBoot应用
2. **请求处理：** Controller接收HTTP请求
3. **业务处理：** Service层处理业务逻辑
4. **数据访问：** Mapper层访问数据库
5. **响应返回：** 通过统一的Result格式返回数据

## 📊 **技术栈总结**

| 技术 | 版本 | 作用 |
|------|------|------|
| SpringBoot | 2.2.4 | 应用框架 |
| MyBatis | 2.1.2 | ORM框架 |
| MySQL | - | 数据库 |
| JWT | 0.9.0 | 身份认证 |
| Lombok | - | 代码简化 |
| FastJSON | 2.0.33 | JSON处理 |
| Apache Commons | 3.12.0 | 工具类库 |
| EasyExcel | 3.2.1 | Excel处理 |

## 📋 **目录重要性排序**

| 排序 | 目录/文件 | 重要性 | 作用描述 |
|------|-----------|--------|----------|
| 1 | `src/main/java/` | ⭐⭐⭐⭐⭐ | 核心业务代码 |
| 2 | `src/main/resources/` | ⭐⭐⭐⭐⭐ | 配置文件和SQL映射 |
| 3 | `pom.xml` | ⭐⭐⭐⭐ | 项目依赖配置 |
| 4 | `pic/` | ⭐⭐⭐ | 文件存储目录 |
| 5 | `target/` | ⭐⭐ | 编译输出（可重新生成） |
| 6 | `book-manage-sys-api.iml` | ⭐ | IDE配置文件 |

## 🛡️ **安全提醒**

- **✅ 可以删除：** `target/`目录（可重新编译生成）
- **⚠️ 谨慎操作：** `pic/`目录（包含用户上传的文件）
- **❌ 绝对不能删除：** `src/`、`pom.xml`等核心文件

这个后端项目采用了标准的**三层架构**设计，代码结构清晰，职责分明，是一个规范的企业级SpringBoot应用。
